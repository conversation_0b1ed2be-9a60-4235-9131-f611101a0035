// Payouts page JavaScript functionality
document.addEventListener('DOMContentLoaded', function () {
    console.log('Payouts page loaded');

    // Initialize payout functionality
    initializePayoutRequests();
    initializePayoutHistory();
});

/**
 * Initialize payout request functionality
 */
function initializePayoutRequests() {
    // Get the payout request button
    const payoutButton = document.querySelector('[aria-haspopup="dialog"]');

    if (payoutButton) {
        payoutButton.addEventListener('click', function (e) {
            e.preventDefault();
            console.log('Payout request button clicked');
            openPayoutModal();
        });
    }
}

/**
 * Initialize payout history functionality
 */
function initializePayoutHistory() {
    // Add any interactive functionality for payout history items
    const payoutItems = document.querySelectorAll('.border.border-border.rounded-lg.p-4');

    payoutItems.forEach(item => {
        item.addEventListener('click', function () {
            console.log('Payout item clicked');
            // Add functionality to view payout details
        });
    });
}

/**
 * Open payout request modal
 */
function openPayoutModal() {
    // Create modal backdrop and container
    const modalHTML = `
        <div id="payoutModalBackdrop" style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(0, 0, 0, 0.5); z-index: 10000; display: flex; align-items: center; justify-content: center; padding: 16px;">
            <div role="dialog" id="payoutModal" aria-describedby="modal-description" aria-labelledby="modal-title"
                style="position: relative; background-color: white; border-radius: 8px; box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); width: 100%; max-width: 28rem; padding: 24px; z-index: 10001; max-height: 90vh; overflow-y: auto;">

                <!-- Close button -->
                <button type="button" id="closeModalBtn"
                    style="position: absolute; right: 16px; top: 16px; color: #9CA3AF; background: none; border: none; cursor: pointer; padding: 4px;">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M18 6 6 18"></path>
                        <path d="m6 6 12 12"></path>
                    </svg>
                </button>

                <!-- Modal header -->
                <div style="margin-bottom: 24px;">
                    <h2 id="modal-title" style="font-size: 20px; font-weight: 600; color: #111827; margin-bottom: 8px;">Request New Payout</h2>
                    <p id="modal-description" style="font-size: 14px; color: #6B7280;">Submit a request to withdraw your profits</p>
                </div>

                <!-- Modal form -->
                <form id="payoutForm" style="display: flex; flex-direction: column; gap: 16px;">
                    <div>
                        <label for="amount" style="display: block; font-size: 14px; font-weight: 500; color: #374151; margin-bottom: 8px;">
                            Amount ($)
                        </label>
                        <input type="number" id="amount" name="amount" min="1" step="0.01" required
                            style="width: 100%; padding: 8px 12px; border: 1px solid #D1D5DB; border-radius: 6px; font-size: 14px; background-color: white; color: #111827; box-sizing: border-box;"
                            placeholder="e.g., 500">
                    </div>

                    <div>
                        <label for="method" style="display: block; font-size: 14px; font-weight: 500; color: #374151; margin-bottom: 8px;">
                            Payout Method
                        </label>
                        <select id="method" name="method" required
                            style="width: 100%; padding: 8px 12px; border: 1px solid #D1D5DB; border-radius: 6px; font-size: 14px; background-color: white; color: #111827; box-sizing: border-box;">
                            <option value="">Select method</option>
                            <option value="bank_transfer">Bank Transfer</option>
                            <option value="paypal">PayPal</option>
                            <option value="crypto">Cryptocurrency</option>
                        </select>
                    </div>

                    <div>
                        <label for="notes" style="display: block; font-size: 14px; font-weight: 500; color: #374151; margin-bottom: 8px;">
                            Notes (Optional)
                        </label>
                        <textarea id="notes" name="notes" rows="3"
                            style="width: 100%; padding: 8px 12px; border: 1px solid #D1D5DB; border-radius: 6px; font-size: 14px; background-color: white; color: #111827; resize: vertical; box-sizing: border-box;"
                            placeholder="Any specific instructions or details..."></textarea>
                    </div>

                    <div style="display: flex; gap: 12px; padding-top: 16px;">
                        <button type="button" id="cancelPayoutBtn"
                            style="flex: 1; padding: 8px 16px; border: 1px solid #D1D5DB; color: #374151; border-radius: 6px; background-color: white; cursor: pointer; font-size: 14px;">
                            Cancel
                        </button>
                        <button type="submit" id="submitPayoutBtn"
                            style="flex: 1; padding: 8px 16px; background-color: #059669; color: white; border-radius: 6px; border: none; cursor: pointer; font-size: 14px;">
                            Submit Payout Request
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Add event listeners for modal
    setupModalEventListeners();
}

/**
 * Setup event listeners for the payout modal
 */
function setupModalEventListeners() {
    const backdrop = document.getElementById('payoutModalBackdrop');
    const closeBtn = document.getElementById('closeModalBtn');
    const form = document.getElementById('payoutForm');

    // Close modal handlers
    if (closeBtn) {
        closeBtn.addEventListener('click', closePayoutModal);
    }

    // Close on backdrop click
    if (backdrop) {
        backdrop.addEventListener('click', closePayoutModal);
    }

    // Close on Escape key
    document.addEventListener('keydown', handleEscapeKey);

    // Handle form submission
    if (form) {
        form.addEventListener('submit', handlePayoutSubmission);
    }
}

/**
 * Close the payout modal
 */
function closePayoutModal() {
    const modal = document.getElementById('payoutModal');
    const backdrop = document.getElementById('payoutModalBackdrop');

    // Remove modal elements
    if (modal) {
        modal.remove();
    }
    if (backdrop) {
        backdrop.remove();
    }

    // Remove the escape key listener
    document.removeEventListener('keydown', handleEscapeKey);
}

/**
 * Handle escape key press
 */
function handleEscapeKey(e) {
    if (e.key === 'Escape') {
        closePayoutModal();
    }
}

/**
 * Handle payout form submission
 */
function handlePayoutSubmission(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const payoutData = {
        amount: formData.get('amount'),
        method: formData.get('method'),
        notes: formData.get('notes')
    };

    console.log('Payout request data:', payoutData);

    // Show loading state
    const submitButton = e.target.querySelector('button[type="submit"]');
    const originalText = submitButton.textContent;
    submitButton.textContent = 'Processing...';
    submitButton.disabled = true;

    // Send the data to Laravel backend
    fetch('/api/payouts', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify(payoutData)
    })
        .then(response => {
            if (!response.ok) {
                return response.json().then(err => Promise.reject(err));
            }
            return response.json();
        })
        .then(data => {
            console.log('Success:', data);
            showNotification(data.message || 'Payout request submitted successfully!', 'success');
            closePayoutModal();

            // Optionally refresh the page to show updated payout history
            // location.reload();
        })
        .catch(error => {
            console.error('Error:', error);

            // Reset button state
            submitButton.textContent = originalText;
            submitButton.disabled = false;

            // Show error message
            let errorMessage = 'Error submitting payout request. Please try again.';
            if (error.errors) {
                // Handle validation errors
                const firstError = Object.values(error.errors)[0];
                if (firstError && firstError.length > 0) {
                    errorMessage = firstError[0];
                }
            } else if (error.message) {
                errorMessage = error.message;
            }

            showNotification(errorMessage, 'error');
        });
}

/**
 * Format currency for display
 */
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

/**
 * Utility function to show notifications
 */
function showNotification(message, type = 'info') {
    // Create a simple notification
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-md text-white z-50 ${type === 'success' ? 'bg-green-600' :
        type === 'error' ? 'bg-red-600' :
            'bg-blue-600'
        }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.remove();
    }, 3000);
}
