// Payouts page JavaScript functionality
document.addEventListener('DOMContentLoaded', function () {
    console.log('Payouts page loaded');

    // Initialize payout functionality
    initializePayoutRequests();
    initializePayoutHistory();
});

/**
 * Initialize payout request functionality
 */
function initializePayoutRequests() {
    // Get the payout request button
    const payoutButton = document.querySelector('[aria-haspopup="dialog"]');

    if (payoutButton) {
        payoutButton.addEventListener('click', function (e) {
            e.preventDefault();
            console.log('Payout request button clicked');
            openPayoutModal();
        });
    }
}

/**
 * Initialize payout history functionality
 */
function initializePayoutHistory() {
    // Add any interactive functionality for payout history items
    const payoutItems = document.querySelectorAll('.border.border-border.rounded-lg.p-4');

    payoutItems.forEach(item => {
        item.addEventListener('click', function () {
            console.log('Payout item clicked');
            // Add functionality to view payout details
        });
    });
}

/**
 * Open payout request modal
 */
function openPayoutModal() {
    // Create modal backdrop and container
    const modalHTML = `
        <div id="payoutModalBackdrop" class="fixed inset-0 bg-black bg-opacity-50 z-40"></div>
        <div role="dialog" id="payoutModal" aria-describedby="modal-description" aria-labelledby="modal-title" data-state="open"
            class="fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg bg-card text-card-foreground border-border"
            tabindex="-1" style="pointer-events: auto;">

            <div class="flex flex-col space-y-1.5 text-center sm:text-left">
                <h2 id="modal-title" class="text-lg font-semibold leading-none tracking-tight">Request New Payout</h2>
                <p id="modal-description" class="text-sm text-muted-foreground">Submit a request to withdraw your profits</p>
            </div>

            <form id="payoutForm" class="space-y-4">
                <div>
                    <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        for="amount">Amount ($)</label>
                    <input
                        class="flex h-10 w-full rounded-md border px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 bg-input border-border text-foreground"
                        id="amount" name="amount" placeholder="e.g., 500" type="number" min="1" step="0.01" required>
                </div>

                <div>
                    <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        for="method">Payout Method</label>
                    <select id="method" name="method" required
                        class="flex h-10 w-full items-center justify-between rounded-md border px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 bg-input border-border text-foreground">
                        <option value="">Select method</option>
                        <option value="bank_transfer">Bank Transfer</option>
                        <option value="paypal">PayPal</option>
                        <option value="crypto">Cryptocurrency</option>
                    </select>
                </div>

                <div>
                    <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        for="notes">Notes (Optional)</label>
                    <textarea
                        class="flex min-h-[80px] w-full rounded-md border px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 bg-input border-border text-foreground"
                        id="notes" name="notes" placeholder="Any specific instructions or details..." rows="3"></textarea>
                </div>

                <button type="submit" id="submitPayoutBtn"
                    class="ring-offset-background focus-visible:outline-hidden focus-visible:ring-ring inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border h-10 px-4 py-2 w-full border-green-500 text-green-400 hover:bg-green-500 hover:text-white bg-transparent">
                    Submit Payout Request
                </button>
            </form>

            <button type="button" id="closeModalBtn"
                class="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-x h-4 w-4">
                    <path d="M18 6 6 18"></path>
                    <path d="m6 6 12 12"></path>
                </svg>
                <span class="sr-only">Close</span>
            </button>
        </div>
    `;

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Add event listeners for modal
    setupModalEventListeners();
}

/**
 * Setup event listeners for the payout modal
 */
function setupModalEventListeners() {
    const backdrop = document.getElementById('payoutModalBackdrop');
    const closeBtn = document.getElementById('closeModalBtn');
    const form = document.getElementById('payoutForm');

    // Close modal handlers
    if (closeBtn) {
        closeBtn.addEventListener('click', closePayoutModal);
    }

    // Close on backdrop click
    if (backdrop) {
        backdrop.addEventListener('click', closePayoutModal);
    }

    // Close on Escape key
    document.addEventListener('keydown', handleEscapeKey);

    // Handle form submission
    if (form) {
        form.addEventListener('submit', handlePayoutSubmission);
    }
}

/**
 * Close the payout modal
 */
function closePayoutModal() {
    const modal = document.getElementById('payoutModal');
    const backdrop = document.getElementById('payoutModalBackdrop');

    // Remove modal elements
    if (modal) {
        modal.remove();
    }
    if (backdrop) {
        backdrop.remove();
    }

    // Remove the escape key listener
    document.removeEventListener('keydown', handleEscapeKey);
}

/**
 * Handle escape key press
 */
function handleEscapeKey(e) {
    if (e.key === 'Escape') {
        closePayoutModal();
    }
}

/**
 * Handle payout form submission
 */
function handlePayoutSubmission(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const payoutData = {
        amount: formData.get('amount'),
        method: formData.get('method'),
        notes: formData.get('notes')
    };

    console.log('Payout request data:', payoutData);

    // Show loading state
    const submitButton = e.target.querySelector('button[type="submit"]');
    const originalText = submitButton.textContent;
    submitButton.textContent = 'Processing...';
    submitButton.disabled = true;

    // Send the data to Laravel backend
    fetch('/api/payouts', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify(payoutData)
    })
        .then(response => {
            if (!response.ok) {
                return response.json().then(err => Promise.reject(err));
            }
            return response.json();
        })
        .then(data => {
            console.log('Success:', data);
            showNotification(data.message || 'Payout request submitted successfully!', 'success');
            closePayoutModal();

            // Optionally refresh the page to show updated payout history
            // location.reload();
        })
        .catch(error => {
            console.error('Error:', error);

            // Reset button state
            submitButton.textContent = originalText;
            submitButton.disabled = false;

            // Show error message
            let errorMessage = 'Error submitting payout request. Please try again.';
            if (error.errors) {
                // Handle validation errors
                const firstError = Object.values(error.errors)[0];
                if (firstError && firstError.length > 0) {
                    errorMessage = firstError[0];
                }
            } else if (error.message) {
                errorMessage = error.message;
            }

            showNotification(errorMessage, 'error');
        });
}

/**
 * Format currency for display
 */
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

/**
 * Utility function to show notifications
 */
function showNotification(message, type = 'info') {
    // Create a simple notification
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-md text-white z-50 ${type === 'success' ? 'bg-green-600' :
        type === 'error' ? 'bg-red-600' :
            'bg-blue-600'
        }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.remove();
    }, 3000);
}
