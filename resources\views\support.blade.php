@extends('components.app')

@section('title', 'Support')

@section('content')
    <main class="flex-1 overflow-y-auto p-6">
        <div class="space-y-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold">Support Center</h1>
                    <p class="text-muted-foreground">Get help with your trading account and platform</p>
                </div><button
                    class="ring-offset-background focus-visible:outline-hidden focus-visible:ring-ring inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border h-10 px-4 py-2 border-green-500 text-green-400 hover:bg-green-500 hover:text-white bg-transparent"
                    type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="radix-_r_1q_"
                    data-state="closed"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round" class="lucide lucide-plus mr-2 h-4 w-4">
                        <path d="M5 12h14"></path>
                        <path d="M12 5v14"></path>
                    </svg>New Ticket</button>
            </div>
            <div class="rounded-lg border shadow-2xs bg-card text-card-foreground border-border" data-v0-t="card">
                <div class="flex flex-col space-y-1.5 p-6">
                    <h3 class="text-2xl font-semibold leading-none tracking-tight">Your Support Tickets</h3>
                    <p class="text-sm text-muted-foreground">Track and manage your support requests</p>
                </div>
                <div class="p-6 pt-0 space-y-4">
                    <div class="border border-border rounded-lg p-4 bg-muted/20">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="font-semibold">Account verification issue</h3>
                            <div class="flex items-center space-x-2">
                                <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent hover:bg-destructive/80 bg-red-600 text-white"
                                    data-v0-t="badge">high</div>
                                <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent hover:bg-secondary/80 bg-yellow-600 text-white"
                                    data-v0-t="badge">in progress</div>
                            </div>
                        </div>
                        <div class="text-sm text-muted-foreground space-y-1">
                            <p>Category: <span class="font-medium">kyc</span></p>
                            <p>Created: 15/01/2024</p>
                            <p>Last Update: 16/01/2024</p>
                        </div>
                    </div>
                    <div class="border border-border rounded-lg p-4 bg-muted/20">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="font-semibold">Platform login issue</h3>
                            <div class="flex items-center space-x-2">
                                <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent hover:bg-secondary/80 bg-orange-600 text-white"
                                    data-v0-t="badge">medium</div>
                                <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent hover:bg-secondary/80 bg-blue-600 text-white"
                                    data-v0-t="badge">open</div>
                            </div>
                        </div>
                        <div class="text-sm text-muted-foreground space-y-1">
                            <p>Category: <span class="font-medium">technical</span></p>
                            <p>Created: 22/01/2024</p>
                            <p>Last Update: 22/01/2024</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="rounded-lg border shadow-2xs bg-card text-card-foreground border-border" data-v0-t="card">
                <div class="flex flex-col space-y-1.5 p-6">
                    <h3 class="text-2xl font-semibold leading-none tracking-tight">Frequently Asked Questions</h3>
                    <p class="text-sm text-muted-foreground">Quick answers to common questions</p>
                </div>
                <div class="p-6 pt-0">
                    <div class="w-full" data-orientation="vertical">
                        <div data-state="closed" data-orientation="vertical" class="border-b border-border">
                            <h3 data-orientation="vertical" data-state="closed" class="flex"><button type="button"
                                    aria-controls="radix-_r_1u_" aria-expanded="false" data-state="closed"
                                    data-orientation="vertical" id="radix-_r_1t_"
                                    class="flex flex-1 items-center justify-between py-4 font-medium transition-all [&amp;[data-state=open]&gt;svg]:rotate-180 text-left text-foreground hover:no-underline"
                                    data-radix-collection-item="">How do I reset my trading account password?<svg
                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="lucide lucide-chevron-down h-4 w-4 shrink-0 transition-transform duration-200">
                                        <path d="m6 9 6 6 6-6"></path>
                                    </svg></button></h3>
                            <div data-state="closed" id="radix-_r_1u_" hidden="" role="region"
                                aria-labelledby="radix-_r_1t_" data-orientation="vertical"
                                class="overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down"
                                style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width);">
                            </div>
                        </div>
                        <div data-state="closed" data-orientation="vertical" class="border-b border-border">
                            <h3 data-orientation="vertical" data-state="closed" class="flex"><button type="button"
                                    aria-controls="radix-_r_20_" aria-expanded="false" data-state="closed"
                                    data-orientation="vertical" id="radix-_r_1v_"
                                    class="flex flex-1 items-center justify-between py-4 font-medium transition-all [&amp;[data-state=open]&gt;svg]:rotate-180 text-left text-foreground hover:no-underline"
                                    data-radix-collection-item="">When will my payout be processed?<svg
                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                        stroke-linecap="round" stroke-linejoin="round"
                                        class="lucide lucide-chevron-down h-4 w-4 shrink-0 transition-transform duration-200">
                                        <path d="m6 9 6 6 6-6"></path>
                                    </svg></button></h3>
                            <div data-state="closed" id="radix-_r_20_" hidden="" role="region"
                                aria-labelledby="radix-_r_1v_" data-orientation="vertical"
                                class="overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down"
                                style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width);">
                            </div>
                        </div>
                        <div data-state="closed" data-orientation="vertical" class="border-b border-border">
                            <h3 data-orientation="vertical" data-state="closed" class="flex"><button type="button"
                                    aria-controls="radix-_r_22_" aria-expanded="false" data-state="closed"
                                    data-orientation="vertical" id="radix-_r_21_"
                                    class="flex flex-1 items-center justify-between py-4 font-medium transition-all [&amp;[data-state=open]&gt;svg]:rotate-180 text-left text-foreground hover:no-underline"
                                    data-radix-collection-item="">What documents are required for KYC verification?<svg
                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                        stroke-linecap="round" stroke-linejoin="round"
                                        class="lucide lucide-chevron-down h-4 w-4 shrink-0 transition-transform duration-200">
                                        <path d="m6 9 6 6 6-6"></path>
                                    </svg></button></h3>
                            <div data-state="closed" id="radix-_r_22_" hidden="" role="region"
                                aria-labelledby="radix-_r_21_" data-orientation="vertical"
                                class="overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down"
                                style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width);">
                            </div>
                        </div>
                        <div data-state="closed" data-orientation="vertical" class="border-b border-border">
                            <h3 data-orientation="vertical" data-state="closed" class="flex"><button type="button"
                                    aria-controls="radix-_r_24_" aria-expanded="false" data-state="closed"
                                    data-orientation="vertical" id="radix-_r_23_"
                                    class="flex flex-1 items-center justify-between py-4 font-medium transition-all [&amp;[data-state=open]&gt;svg]:rotate-180 text-left text-foreground hover:no-underline"
                                    data-radix-collection-item="">How do I track my challenge progress?<svg
                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                        stroke-linecap="round" stroke-linejoin="round"
                                        class="lucide lucide-chevron-down h-4 w-4 shrink-0 transition-transform duration-200">
                                        <path d="m6 9 6 6 6-6"></path>
                                    </svg></button></h3>
                            <div data-state="closed" id="radix-_r_24_" hidden="" role="region"
                                aria-labelledby="radix-_r_23_" data-orientation="vertical"
                                class="overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down"
                                style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width);">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
@endsection
