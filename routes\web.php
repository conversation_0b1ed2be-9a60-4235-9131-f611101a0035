<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ChallengeController;
use App\Http\Controllers\KYCController;

Route::get('/', [DashboardController::class, 'index'])->name('dashboard');
Route::get('/challenges', [ChallengeController::class, 'index'])->name('challenges');
Route::get('/payouts', [App\Http\Controllers\PayoutRequestsController::class, 'index'])->name('payouts');
Route::post('/api/payouts', [App\Http\Controllers\PayoutRequestsController::class, 'store'])->name('payouts.store');
Route::get('/kyc', [KYCController::class, 'index'])->name('kyc');
Route::get('/profile', [App\Http\Controllers\ProfileController::class, 'index'])->name('profile');
Route::get('/support', [App\Http\Controllers\SupportController::class, 'index'])->name('support');