<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class DashboardController extends Controller
{
    public function index()
    {
        // Mock stats (in real app, fetch from DB)
        $stats = [
            'totalChallenges' => 1,
            'activeChallenges' => 1,
            'completedChallenges' => 0,
            'totalProfit' => -204.10,
            'winRate' => 33,
            'balance' => 4795.90,
            'equity' => 4883.10,
            'drawdown' => 29,
            'openTrades' => 6,
            'closedTrades' => 120,
        ];

        // Mock trading positions
        $tradingPositions = [
            [
                'ticket' => '5836514',
                'symbol' => 'XAUUSD.JC',
                'openTime' => '2025-07-21 06:56:21',
                'closeTime' => 'N/A',
                'closePrice' => 0,
                'openPrice' => 3367.71,
                'type' => 'Sell',
                'volume' => 0.2,
                'profitLoss' => 0,
                'holdTime' => '3 hours, 41 minutes, 41 seconds',
            ],
            [
                'ticket' => '4878165',
                'symbol' => 'XAUUSD.JC',
                'openTime' => '2025-07-18 08:16:07',
                'closeTime' => '2025-07-18 08:07:05',
                'closePrice' => 3351.22,
                'openPrice' => 3346.63,
                'type' => 'Sell',
                'volume' => 0.1,
                'profitLoss' => -45.8,
                'holdTime' => '50 minutes, 58 seconds',
            ],
            [
                'ticket' => '4854925',
                'symbol' => 'EURUSD.JC',
                'openTime' => '2025-07-18 06:56:19',
                'closeTime' => '2025-07-18 08:01:21',
                'closePrice' => 1.16,
                'openPrice' => 1.16,
                'type' => 'Sell',
                'volume' => 0.3,
                'profitLoss' => -49.8,
                'holdTime' => '1 hour, 5 minutes, 2 seconds',
            ],
        ];

        return view('dashboard', compact('stats', 'tradingPositions'));
    }
}
