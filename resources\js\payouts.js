// Payouts page JavaScript functionality
document.addEventListener('DOMContentLoaded', function() {
    console.log('Payouts page loaded');
    
    // Initialize payout functionality
    initializePayoutRequests();
    initializePayoutHistory();
});

/**
 * Initialize payout request functionality
 */
function initializePayoutRequests() {
    // Get the payout request button
    const payoutButton = document.querySelector('[aria-haspopup="dialog"]');
    
    if (payoutButton) {
        payoutButton.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Payout request button clicked');
            openPayoutModal();
        });
    }
}

/**
 * Initialize payout history functionality
 */
function initializePayoutHistory() {
    // Add any interactive functionality for payout history items
    const payoutItems = document.querySelectorAll('.border.border-border.rounded-lg.p-4');
    
    payoutItems.forEach(item => {
        item.addEventListener('click', function() {
            console.log('Payout item clicked');
            // Add functionality to view payout details
        });
    });
}

/**
 * Open payout request modal
 */
function openPayoutModal() {
    // Create modal HTML
    const modalHTML = `
        <div id="payoutModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-bold">Request Payout</h2>
                    <button id="closeModal" class="text-gray-500 hover:text-gray-700">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                
                <form id="payoutForm">
                    <div class="mb-4">
                        <label for="amount" class="block text-sm font-medium mb-2">Amount ($)</label>
                        <input type="number" id="amount" name="amount" min="1" step="0.01" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                               placeholder="Enter amount" required>
                    </div>
                    
                    <div class="mb-4">
                        <label for="method" class="block text-sm font-medium mb-2">Payout Method</label>
                        <select id="method" name="method" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500" required>
                            <option value="">Select method</option>
                            <option value="bank_transfer">Bank Transfer</option>
                            <option value="paypal">PayPal</option>
                            <option value="crypto">Cryptocurrency</option>
                        </select>
                    </div>
                    
                    <div class="mb-6">
                        <label for="notes" class="block text-sm font-medium mb-2">Notes (Optional)</label>
                        <textarea id="notes" name="notes" rows="3" 
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                                  placeholder="Additional notes..."></textarea>
                    </div>
                    
                    <div class="flex gap-3">
                        <button type="button" id="cancelPayout" 
                                class="flex-1 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">
                            Cancel
                        </button>
                        <button type="submit" 
                                class="flex-1 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                            Request Payout
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;
    
    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    
    // Add event listeners for modal
    setupModalEventListeners();
}

/**
 * Setup event listeners for the payout modal
 */
function setupModalEventListeners() {
    const modal = document.getElementById('payoutModal');
    const closeBtn = document.getElementById('closeModal');
    const cancelBtn = document.getElementById('cancelPayout');
    const form = document.getElementById('payoutForm');
    
    // Close modal handlers
    [closeBtn, cancelBtn].forEach(btn => {
        btn.addEventListener('click', closePayoutModal);
    });
    
    // Close on backdrop click
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closePayoutModal();
        }
    });
    
    // Handle form submission
    form.addEventListener('submit', handlePayoutSubmission);
}

/**
 * Close the payout modal
 */
function closePayoutModal() {
    const modal = document.getElementById('payoutModal');
    if (modal) {
        modal.remove();
    }
}

/**
 * Handle payout form submission
 */
function handlePayoutSubmission(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const payoutData = {
        amount: formData.get('amount'),
        method: formData.get('method'),
        notes: formData.get('notes')
    };
    
    console.log('Payout request data:', payoutData);
    
    // Here you would typically send the data to your Laravel backend
    // Example AJAX call:
    /*
    fetch('/api/payouts', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify(payoutData)
    })
    .then(response => response.json())
    .then(data => {
        console.log('Success:', data);
        closePayoutModal();
        // Refresh the page or update the payout history
        location.reload();
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error submitting payout request. Please try again.');
    });
    */
    
    // For now, just show a success message and close modal
    alert('Payout request submitted successfully!');
    closePayoutModal();
}

/**
 * Format currency for display
 */
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

/**
 * Utility function to show notifications
 */
function showNotification(message, type = 'info') {
    // Create a simple notification
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-md text-white z-50 ${
        type === 'success' ? 'bg-green-600' : 
        type === 'error' ? 'bg-red-600' : 
        'bg-blue-600'
    }`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.remove();
    }, 3000);
}


console.log('Payouts page loaded');
