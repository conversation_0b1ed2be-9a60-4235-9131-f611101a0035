@extends('components.app')

@section('title', 'KYC');

@section('content')
    <main class="flex-1 overflow-y-auto p-6">
        <div class="space-y-4 sm:space-y-6">
            <div>
                <h1 class="text-2xl sm:text-3xl font-bold">KYC Documents</h1>
                <p class="text-sm sm:text-base text-muted-foreground">Upload your identity verification documents</p>
            </div>
            <div class="grid gap-4 sm:gap-6 grid-cols-1 md:grid-cols-2 xl:grid-cols-3">
                <div class="rounded-lg border shadow-2xs bg-card text-card-foreground border-border" data-v0-t="card">
                    <div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2">
                        <div class="flex items-center space-x-2"><svg xmlns="http://www.w3.org/2000/svg" width="24"
                                height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                stroke-linecap="round" stroke-linejoin="round"
                                class="lucide lucide-file-text h-6 w-6 text-muted-foreground">
                                <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path>
                                <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
                                <path d="M10 9H8"></path>
                                <path d="M16 13H8"></path>
                                <path d="M16 17H8"></path>
                            </svg>
                            <h3 class="font-semibold tracking-tight text-base sm:text-lg">CNIC Front</h3>
                        </div>
                        <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent hover:bg-primary/80 bg-green-600 text-white"
                            data-v0-t="badge">approved</div>
                    </div>
                    <div class="p-6 pt-0 space-y-4">
                        <p class="text-xs sm:text-sm text-muted-foreground">Upload the front side of your CNIC</p>
                        <div class="text-xs sm:text-sm text-muted-foreground space-y-1">
                            <p>Status: <span class="font-medium">approved</span></p>
                            <p>Uploaded: 15/01/2024</p>
                            <p>Reviewed: 16/01/2024</p>
                        </div><input class="hidden" accept=".jpg,.jpeg,.png,.pdf" type="file"><button
                            class="ring-offset-background focus-visible:outline-hidden focus-visible:ring-ring inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 bg-background border h-10 px-4 py-2 w-full border-green-500 text-green-400 hover:bg-green-500 hover:text-white text-xs sm:text-sm">Replace
                            Document</button>
                    </div>
                </div>
                <div class="rounded-lg border shadow-2xs bg-card text-card-foreground border-border" data-v0-t="card">
                    <div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2">
                        <div class="flex items-center space-x-2"><svg xmlns="http://www.w3.org/2000/svg" width="24"
                                height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                stroke-linecap="round" stroke-linejoin="round"
                                class="lucide lucide-file-text h-6 w-6 text-muted-foreground">
                                <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path>
                                <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
                                <path d="M10 9H8"></path>
                                <path d="M16 13H8"></path>
                                <path d="M16 17H8"></path>
                            </svg>
                            <h3 class="font-semibold tracking-tight text-base sm:text-lg">CNIC Back</h3>
                        </div>
                        <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent hover:bg-secondary/80 bg-yellow-600 text-white"
                            data-v0-t="badge">pending</div>
                    </div>
                    <div class="p-6 pt-0 space-y-4">
                        <p class="text-xs sm:text-sm text-muted-foreground">Upload the back side of your CNIC</p>
                        <div class="text-xs sm:text-sm text-muted-foreground space-y-1">
                            <p>Status: <span class="font-medium">pending</span></p>
                            <p>Uploaded: 15/01/2024</p>
                        </div><input class="hidden" accept=".jpg,.jpeg,.png,.pdf" type="file"><button
                            class="ring-offset-background focus-visible:outline-hidden focus-visible:ring-ring inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 bg-background border h-10 px-4 py-2 w-full border-green-500 text-green-400 hover:bg-green-500 hover:text-white text-xs sm:text-sm"
                            disabled="">Upload Document</button>
                    </div>
                </div>
                <div class="rounded-lg border shadow-2xs bg-card text-card-foreground border-border md:col-span-2 xl:col-span-1"
                    data-v0-t="card">
                    <div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2">
                        <div class="flex items-center space-x-2"><svg xmlns="http://www.w3.org/2000/svg" width="24"
                                height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                stroke-linecap="round" stroke-linejoin="round"
                                class="lucide lucide-camera h-6 w-6 text-muted-foreground">
                                <path
                                    d="M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z">
                                </path>
                                <circle cx="12" cy="13" r="3"></circle>
                            </svg>
                            <h3 class="font-semibold tracking-tight text-base sm:text-lg">Selfie with CNIC</h3>
                        </div>
                        <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent hover:bg-destructive/80 bg-red-600 text-white"
                            data-v0-t="badge">Not Uploaded</div>
                    </div>
                    <div class="p-6 pt-0 space-y-4">
                        <p class="text-xs sm:text-sm text-muted-foreground">Upload a selfie holding your CNIC</p><button
                            class="ring-offset-background focus-visible:outline-hidden focus-visible:ring-ring inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border h-10 px-4 py-2 w-full border-green-500 text-green-400 hover:bg-green-500 hover:text-white bg-transparent text-xs sm:text-sm">Upload
                            Document</button>
                    </div>
                </div>
            </div>
            <div class="rounded-lg border shadow-2xs bg-card text-card-foreground border-border" data-v0-t="card">
                <div class="flex flex-col space-y-1.5 p-6">
                    <h3 class="font-semibold tracking-tight text-lg sm:text-xl">KYC Guidelines</h3>
                    <p class="text-muted-foreground text-xs sm:text-sm">Please follow these guidelines for successful
                        verification</p>
                </div>
                <div class="p-6 pt-0 space-y-4 text-xs sm:text-sm text-muted-foreground">
                    <div>
                        <h3 class="font-semibold text-foreground mb-2">Document Requirements:</h3>
                        <ul class="list-disc pl-4 sm:pl-5 space-y-1">
                            <li>Documents must be clear and readable</li>
                            <li>All four corners of the CNIC must be visible</li>
                            <li>No blurry or dark images</li>
                            <li>File size should be less than 5MB</li>
                            <li>Accepted formats: JPG, PNG, PDF</li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="font-semibold text-foreground mb-2">Selfie Requirements:</h3>
                        <ul class="list-disc pl-4 sm:pl-5 space-y-1">
                            <li>Hold your CNIC next to your face</li>
                            <li>Ensure both your face and CNIC are clearly visible</li>
                            <li>Good lighting is essential</li>
                            <li>No sunglasses or face coverings</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </main>
@endsection
