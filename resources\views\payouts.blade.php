@extends('components.app')

@section('title', 'Payout');

@section('content')
    <main class="flex-1 overflow-y-auto p-6">
        <div class="space-y-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold">Payout Requests</h1>
                    <p class="text-muted-foreground">Manage your profit withdrawals</p>
                </div><button
                    class="ring-offset-background focus-visible:outline-hidden focus-visible:ring-ring inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border h-10 px-4 py-2 border-green-500 text-green-400 hover:bg-green-500 hover:text-white bg-transparent"
                    type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="radix-_r_17_"
                    data-state="closed"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round" class="lucide lucide-plus mr-2 h-4 w-4">
                        <path d="M5 12h14"></path>
                        <path d="M12 5v14"></path>
                    </svg>Request Payout</button>
            </div>
            <div class="rounded-lg border shadow-2xs bg-card text-card-foreground border-border" data-v0-t="card">
                <div class="flex flex-col space-y-1.5 p-6">
                    <h3 class="text-2xl font-semibold leading-none tracking-tight">Payout History</h3>
                    <p class="text-sm text-muted-foreground">Track your payout requests and their status</p>
                </div>
                <div class="p-6 pt-0 space-y-4">
                    <div class="border border-border rounded-lg p-4 bg-muted/20">
                        <div class="flex items-center justify-between mb-2">
                            <div class="flex items-center space-x-2"><svg xmlns="http://www.w3.org/2000/svg" width="24"
                                    height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round"
                                    class="lucide lucide-banknote h-5 w-5 text-muted-foreground">
                                    <rect width="20" height="12" x="2" y="6" rx="2"></rect>
                                    <circle cx="12" cy="12" r="2"></circle>
                                    <path d="M6 12h.01M18 12h.01"></path>
                                </svg><span class="text-lg font-semibold">$5000.00</span><span
                                    class="text-sm text-muted-foreground">via Bank Transfer</span></div>
                            <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent hover:bg-primary/80 bg-green-600 text-white"
                                data-v0-t="badge">completed</div>
                        </div>
                        <div class="text-sm text-muted-foreground space-y-1">
                            <p>Requested: 15/01/2024</p>
                            <p>Completed: 18/01/2024</p>
                            <p>Notes: First payout completed successfully</p>
                        </div>
                    </div>
                    <div class="border border-border rounded-lg p-4 bg-muted/20">
                        <div class="flex items-center justify-between mb-2">
                            <div class="flex items-center space-x-2"><svg xmlns="http://www.w3.org/2000/svg" width="24"
                                    height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round"
                                    class="lucide lucide-shopping-cart h-5 w-5 text-muted-foreground">
                                    <circle cx="8" cy="21" r="1"></circle>
                                    <circle cx="19" cy="21" r="1"></circle>
                                    <path
                                        d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12">
                                    </path>
                                </svg><span class="text-lg font-semibold">$3000.00</span><span
                                    class="text-sm text-muted-foreground">via PayPal</span></div>
                            <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent hover:bg-secondary/80 bg-yellow-600 text-white"
                                data-v0-t="badge">pending</div>
                        </div>
                        <div class="text-sm text-muted-foreground space-y-1">
                            <p>Requested: 20/01/2024</p>
                            <p>Notes: Awaiting approval</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
@endsection

@section('scripts')
<script src="{{ asset('js/payouts.js') }}"></script>
@endsection
